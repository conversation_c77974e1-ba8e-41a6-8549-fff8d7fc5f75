/**
 * Utility functions for managing socket authentication credentials
 */

export interface SocketCredentials {
  userId: string;
  password: string;
}

/**
 * Get socket credentials from localStorage
 */
export const getSocketCredentials = (): SocketCredentials | null => {
  if (typeof window === 'undefined') {
    return null; // Server-side rendering
  }

  try {
    const userId = localStorage.getItem('userId');
    const password = localStorage.getItem('userPassword');

    if (!userId || !password) {
      console.warn('Socket credentials not found in localStorage');
      return null;
    }

    return {
      userId,
      password,
    };
  } catch (error) {
    console.error('Error reading socket credentials from localStorage:', error);
    return null;
  }
};

/**
 * Set socket credentials in localStorage
 */
export const setSocketCredentials = (credentials: SocketCredentials): boolean => {
  if (typeof window === 'undefined') {
    return false; // Server-side rendering
  }

  try {
    localStorage.setItem('userId', credentials.userId);
    localStorage.setItem('userPassword', credentials.password);
    console.log('Socket credentials saved to localStorage');
    return true;
  } catch (error) {
    console.error('Error saving socket credentials to localStorage:', error);
    return false;
  }
};

/**
 * Clear socket credentials from localStorage
 */
export const clearSocketCredentials = (): boolean => {
  if (typeof window === 'undefined') {
    return false; // Server-side rendering
  }

  try {
    localStorage.removeItem('userId');
    localStorage.removeItem('userPassword');
    console.log('Socket credentials cleared from localStorage');
    return true;
  } catch (error) {
    console.error('Error clearing socket credentials from localStorage:', error);
    return false;
  }
};

/**
 * Check if socket credentials exist in localStorage
 */
export const hasSocketCredentials = (): boolean => {
  if (typeof window === 'undefined') {
    return false; // Server-side rendering
  }

  const userId = localStorage.getItem('userId');
  const password = localStorage.getItem('userPassword');
  
  return !!(userId && password);
};
