"use client";

import { useState } from "react";
import { Search, RefreshCw, User, Mail } from "lucide-react";
import Avatar from "../common/Avatar";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useConnectedUsers, ConnectedUser } from "@/hooks/useConnectedUsers";

interface ConnectedUsersListProps {
  onSelectUser: (user: ConnectedUser) => void;
  selectedUserId?: number;
}

const ConnectedUsersList = ({ onSelectUser, selectedUserId }: ConnectedUsersListProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const { users, loading, error, refetch } = useConnectedUsers();

  const filteredUsers = users.filter((user) =>
    `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleUserClick = (user: ConnectedUser) => {
    onSelectUser(user);
  };

  if (error) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Connected Users</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={refetch}
              className="h-8 w-8"
              title="Refresh"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-3 bg-red-100 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-1">Error Loading Users</h3>
            <p className="text-xs text-gray-500 mb-3">{error}</p>
            <Button onClick={refetch} size="sm" variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Connected Users</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={refetch}
            disabled={loading}
            className="h-8 w-8"
            title="Refresh"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-white border-gray-200"
          />
        </div>
      </div>

      {/* Users List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 mx-auto mb-2 text-gray-400 animate-spin" />
              <p className="text-sm text-gray-500">Loading connected users...</p>
            </div>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <User className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-500">
                {searchQuery ? "No users found" : "No connected users"}
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredUsers.map((user) => (
              <div
                key={user.id}
                onClick={() => handleUserClick(user)}
                className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                  selectedUserId === user.id ? "bg-blue-50 border-r-2 border-blue-500" : ""
                }`}
              >
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <Avatar
                    profileImg=""
                    name={`${user.firstName.charAt(0)}${user.lastName.charAt(0)}`}
                    styles="h-10 w-10 bg-slate-700 text-white flex-shrink-0"
                  />

                  {/* User Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {user.firstName} {user.lastName}
                      </h3>
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                        Connected
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Mail className="w-3 h-3" />
                      <span className="truncate">{user.email}</span>
                    </div>

                    {user.galleries && user.galleries.length > 0 && (
                      <div className="mt-1">
                        <span className="text-xs text-blue-600">
                          {user.galleries.length} gallery{user.galleries.length !== 1 ? 'ies' : 'y'}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* User ID Badge */}
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                      ID: {user.id}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer Info */}
      {!loading && filteredUsers.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <p className="text-xs text-gray-500 text-center">
            {filteredUsers.length} user{filteredUsers.length !== 1 ? 's' : ''} 
            {searchQuery && ` matching "${searchQuery}"`}
          </p>
        </div>
      )}
    </div>
  );
};

export default ConnectedUsersList;
