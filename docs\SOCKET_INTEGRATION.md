# Socket Integration Guide

This document explains how to use the real-time messaging system with Socket.IO integration.

## Overview

The messaging system connects to `https://api.engageathlete.com/socket.io/` for real-time communication. It uses credentials stored in localStorage for authentication.

## Setup Instructions

### 1. Configure Credentials

When you first visit the messages page, you'll see a setup screen asking for:
- **User ID**: Your unique user identifier
- **Password**: Your authentication password

These credentials are stored securely in your browser's localStorage.

### 2. Connection Process

The socket connection:
- Connects to: `https://api.engageathlete.com`
- Uses path: `/socket.io/`
- Transports: `['polling', 'websocket']`
- Authentication: User ID and Password from localStorage

### 3. Features

#### Real-time Messaging
- ✅ Send and receive messages instantly
- ✅ File attachments (images, PDFs, documents)
- ✅ Link detection and rendering
- ✅ Message read receipts

#### Typing Indicators
- ✅ See when someone is typing
- ✅ Animated typing dots
- ✅ Auto-hide after 1 second of inactivity

#### Online Status
- ✅ Real-time online/offline status
- ✅ Last seen timestamps
- ✅ Connection status indicators

#### Connection Management
- ✅ Auto-reconnection on disconnect
- ✅ Connection status monitoring
- ✅ Error handling and logging

## Socket Events

### Outgoing Events (Client → Server)

```typescript
// Get initial conversations
socket.emit('get_conversations');

// Get messages for a conversation
socket.emit('get_conversation_messages', { conversationId });

// Send a message
socket.emit('send_message', {
  id: messageId,
  conversationId,
  content,
  attachment?: { ... }
});

// Mark messages as read
socket.emit('mark_messages_read', { conversationId });

// Send typing indicator
socket.emit('typing', { conversationId, isTyping });
```

### Incoming Events (Server → Client)

```typescript
// Initial conversations list
socket.on('conversations_list', (conversations) => { ... });

// Messages for a conversation
socket.on('conversation_messages', ({ conversationId, messages }) => { ... });

// New message received
socket.on('new_message', (message) => { ... });

// User typing status
socket.on('user_typing', ({ conversationId, userId, userName, isTyping }) => { ... });

// Message read receipts
socket.on('messages_read', ({ conversationId, userId }) => { ... });

// User online status changes
socket.on('user_status_changed', ({ userId, isOnline }) => { ... });

// Online users list
socket.on('users_online', (userIds) => { ... });
```

## Authentication

The socket connection uses the following authentication data:

```typescript
{
  auth: {
    userId: "user_id_from_localStorage",
    password: "password_from_localStorage", 
    userRole: session.user.roleId,
    userName: session.user.userFirstName + ' ' + session.user.userLastName
  }
}
```

## Error Handling

The system includes comprehensive error handling:
- Connection errors are logged to console
- Automatic reconnection attempts (up to 5 times)
- Fallback to mock data if socket fails
- User-friendly error messages

## Development

### Testing Socket Connection

1. Open browser developer tools
2. Go to Console tab
3. Look for socket connection logs:
   ```
   Socket connected to engageathlete.com: [socket_id]
   Auth data: { userId: "...", userRole: "...", userName: "..." }
   ```

### Mock Data Fallback

If socket connection fails, the system falls back to mock data for development:
- 15 sample conversations
- Various message types (text, links, attachments)
- Simulated online/offline status

## Troubleshooting

### Connection Issues

1. **Check credentials**: Ensure userId and password are set in localStorage
2. **Network connectivity**: Verify internet connection
3. **CORS issues**: Check browser console for CORS errors
4. **Server status**: Verify the socket server is running

### Common Problems

- **"Disconnected" status**: Check credentials and network
- **No conversations loading**: Verify socket events are being received
- **Messages not sending**: Check connection status and authentication

### Debug Mode

Enable debug logging by adding to localStorage:
```javascript
localStorage.setItem('debug', 'socket.io-client:*');
```

## Security

- Credentials are stored in browser localStorage only
- Socket connection uses HTTPS/WSS
- Authentication required for all socket operations
- No sensitive data logged in production
