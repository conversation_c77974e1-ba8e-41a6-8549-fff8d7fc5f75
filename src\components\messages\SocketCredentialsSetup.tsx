"use client";

import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { setSocketCredentials, getSocketCredentials, hasSocketCredentials } from '@/utils/socketAuth';
import { useSocket } from '@/contexts/SocketContext';

const SocketCredentialsSetup = () => {
  const [userId, setUserId] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [hasCredentials, setHasCredentials] = useState(false);
  const { isConnected } = useSocket();

  useEffect(() => {
    // Check if credentials already exist
    const credentialsExist = hasSocketCredentials();
    setHasCredentials(credentialsExist);

    if (credentialsExist) {
      const credentials = getSocketCredentials();
      if (credentials) {
        setUserId(credentials.userId);
        setPassword('••••••••'); // Mask the password
      }
    }
  }, []);

  const handleSave = async () => {
    if (!userId.trim() || !password.trim()) {
      setMessage('Please enter both User ID and Password');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const success = setSocketCredentials({
        userId: userId.trim(),
        password: password.trim(),
      });

      if (success) {
        setMessage('Credentials saved successfully! Please refresh the page to connect.');
        setHasCredentials(true);
      } else {
        setMessage('Failed to save credentials. Please try again.');
      }
    } catch (error) {
      console.error('Error saving credentials:', error);
      setMessage('An error occurred while saving credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setUserId('');
    setPassword('');
    setHasCredentials(false);
    setMessage('');
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>Socket Connection Setup</span>
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
        </CardTitle>
        <CardDescription>
          Configure your credentials for real-time messaging
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="userId" className="text-sm font-medium">
            User ID
          </label>
          <Input
            id="userId"
            type="text"
            placeholder="Enter your user ID"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="password" className="text-sm font-medium">
            Password
          </label>
          <Input
            id="password"
            type="password"
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
          />
        </div>

        {message && (
          <div className={`text-sm p-3 rounded ${
            message.includes('successfully') 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {message}
          </div>
        )}

        <div className="flex space-x-2">
          <Button 
            onClick={handleSave} 
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? 'Saving...' : hasCredentials ? 'Update' : 'Save'}
          </Button>
          
          {hasCredentials && (
            <Button 
              variant="outline" 
              onClick={handleClear}
              disabled={isLoading}
            >
              Clear
            </Button>
          )}
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Connection Status:</strong> {isConnected ? 'Connected' : 'Disconnected'}</p>
          <p><strong>Endpoint:</strong> https://api.engageathlete.com/socket.io/</p>
          <p><strong>Note:</strong> Credentials are stored in your browser's localStorage</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SocketCredentialsSetup;
