# Connected Users API Integration

This document explains how the connected users API integration works in the messaging system.

## Overview

The system fetches connected users from the API endpoint and displays them in the messages screen, allowing users to start conversations with connected users.

## API Endpoint

```
GET https://api.engageathlete.com/api/notification/v1/messages/connecteduser/{userId}
```

### Parameters
- `userId`: Retrieved from localStorage (key: 'userId')

### Response Format
```json
[
    {
        "id": 145,
        "firstName": "First",
        "lastName": "Athlete", 
        "email": "<EMAIL>",
        "galleries": []
    }
]
```

## Implementation

### 1. useConnectedUsers Hook (`src/hooks/useConnectedUsers.ts`)
- Fetches connected users from the API
- Uses userId from localStorage
- Handles loading states and errors
- Provides refetch functionality

### 2. ConnectedUsersList Component (`src/components/messages/ConnectedUsersList.tsx`)
- Displays the list of connected users
- Search functionality
- User selection handling
- Loading and error states
- Refresh button

### 3. UserIdSetup Component (`src/components/messages/UserIdSetup.tsx`)
- Allows users to configure their userId
- Saves to localStorage
- Test API functionality
- Clear/update userId

### 4. MessagesScreen Integration
- Checks for userId in localStorage on load
- Shows setup screen if userId not found
- Toggles between Users and Chats view
- Settings button to reconfigure userId

## Usage Flow

### First Time Setup
1. User visits Messages page
2. If no userId in localStorage, shows UserIdSetup component
3. User enters their userId (e.g., 170)
4. System saves to localStorage and fetches connected users

### Normal Usage
1. User clicks "Users" tab to see connected users
2. System fetches from API using stored userId
3. User can search and select users to start conversations
4. Click "Settings" to reconfigure userId if needed

## Features

### ✅ Connected Users Display
- Real-time API data fetching
- User search functionality
- User details (name, email, galleries count)
- Online status indicators
- User ID badges

### ✅ Error Handling
- Network error handling
- Invalid userId handling
- Loading states
- Retry functionality

### ✅ User Management
- localStorage userId storage
- Easy userId configuration
- Test API functionality
- Clear/update options

### ✅ UI/UX Features
- Responsive design
- Search functionality
- Loading indicators
- Error messages
- Settings access

## API Integration Details

### Request Headers
```javascript
{
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
```

### Error Handling
- HTTP status code validation
- Network error handling
- User-friendly error messages
- Console logging for debugging

### Data Processing
- Converts API response to internal format
- Handles empty responses
- Type safety with TypeScript interfaces

## Testing

### Manual Testing
1. Set userId in localStorage: `localStorage.setItem('userId', '170')`
2. Visit Messages page
3. Click "Users" tab
4. Verify API call in Network tab
5. Check connected users display

### API Testing
1. Use UserIdSetup component "Test API Call" button
2. Check browser console for response
3. Verify error handling with invalid userId

## Configuration

### localStorage Keys
- `userId`: User ID for API calls

### Environment
- API endpoint is hardcoded to production
- No environment variables needed

## Troubleshooting

### Common Issues
1. **No users loading**: Check userId in localStorage
2. **API errors**: Verify network connection and userId validity
3. **Empty list**: User may have no connected users

### Debug Steps
1. Check browser console for errors
2. Verify localStorage userId value
3. Test API call manually
4. Check Network tab for API requests

## Future Enhancements

### Potential Improvements
- Caching connected users data
- Real-time updates
- Pagination for large user lists
- User status indicators
- Profile pictures integration
