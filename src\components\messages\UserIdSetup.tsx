"use client";

import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

const UserIdSetup = () => {
  const [userId, setUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [hasUserId, setHasUserId] = useState(false);

  useEffect(() => {
    // Check if userId already exists
    const storedUserId = localStorage.getItem('userId');
    if (storedUserId) {
      setUserId(storedUserId);
      setHasUserId(true);
    }
  }, []);

  const handleSave = async () => {
    if (!userId.trim()) {
      setMessage('Please enter a valid User ID');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      // Save to localStorage
      localStorage.setItem('userId', userId.trim());
      setHasUserId(true);
      setMessage('User ID saved successfully! You can now view connected users.');
    } catch (error) {
      console.error('Error saving user ID:', error);
      setMessage('Failed to save User ID. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    localStorage.removeItem('userId');
    setUserId('');
    setHasUserId(false);
    setMessage('User ID cleared successfully.');
  };

  const testApiCall = async () => {
    if (!userId.trim()) {
      setMessage('Please enter a User ID first');
      return;
    }

    setIsLoading(true);
    setMessage('Testing API call...');

    try {
      const response = await fetch(
        `https://api.engageathlete.com/api/notification/v1/messages/connecteduser/${userId.trim()}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data);
      setMessage(`API call successful! Found ${data.length} connected users. Check console for details.`);
    } catch (error) {
      console.error('API call failed:', error);
      setMessage(`API call failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>User ID Setup</CardTitle>
        <CardDescription>
          Configure your User ID to fetch connected users from the API
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="userId" className="text-sm font-medium">
            User ID
          </label>
          <Input
            id="userId"
            type="text"
            placeholder="Enter your user ID (e.g., 170)"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500">
            This will be used in the API call: /connecteduser/{userId}
          </p>
        </div>

        {message && (
          <div className={`text-sm p-3 rounded ${
            message.includes('successful') 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : message.includes('failed') || message.includes('error')
              ? 'bg-red-50 text-red-700 border border-red-200'
              : 'bg-blue-50 text-blue-700 border border-blue-200'
          }`}>
            {message}
          </div>
        )}

        <div className="flex space-x-2">
          <Button 
            onClick={handleSave} 
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? 'Saving...' : hasUserId ? 'Update' : 'Save'}
          </Button>
          
          {hasUserId && (
            <Button 
              variant="outline" 
              onClick={handleClear}
              disabled={isLoading}
            >
              Clear
            </Button>
          )}
        </div>

        {userId && (
          <Button 
            onClick={testApiCall} 
            disabled={isLoading}
            variant="secondary"
            className="w-full"
          >
            {isLoading ? 'Testing...' : 'Test API Call'}
          </Button>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>API Endpoint:</strong></p>
          <p className="font-mono bg-gray-100 p-2 rounded text-xs break-all">
            https://api.engageathlete.com/api/notification/v1/messages/connecteduser/{userId || '{userId}'}
          </p>
          <p><strong>Current User ID:</strong> {hasUserId ? userId : 'Not set'}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserIdSetup;
