"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { io, Socket } from 'socket.io-client';
import { getSocketCredentials } from '@/utils/socketAuth';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  onlineUsers: string[];
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  onlineUsers: [],
});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  const { data: session } = useSession();

  useEffect(() => {
    if (session?.user?.id) {
      // Get credentials from localStorage
      const credentials = getSocketCredentials();
      const userId = credentials?.userId || session.user.id;
      const password = credentials?.password || '';

      if (!credentials) {
        console.warn('No socket credentials found. Please ensure userId and userPassword are set in localStorage.');
      }

      // Initialize socket connection to the API endpoint
      const socketInstance = io('https://api.engageathlete.com', {
        path: '/socket.io/',
        auth: {
          userId: userId,
          password: password,
          userRole: session.user.roleId,
          userName: session.user.userFirstName + ' ' + session.user.userLastName,
        },
        transports: ['polling', 'websocket'],
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 20000,
        withCredentials: true,
      });

      // Connection event handlers
      socketInstance.on('connect', () => {
        console.log('Socket connected to engageathlete.com:', socketInstance.id);
        console.log('Auth data:', {
          userId: userId,
          userRole: session.user.roleId,
          userName: session.user.userFirstName + ' ' + session.user.userLastName,
        });
        setIsConnected(true);
      });

      socketInstance.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        setIsConnected(false);
      });

      socketInstance.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        console.error('Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        setIsConnected(false);
      });

      socketInstance.on('reconnect', (attemptNumber) => {
        console.log('Socket reconnected after', attemptNumber, 'attempts');
        setIsConnected(true);
      });

      socketInstance.on('reconnect_error', (error) => {
        console.error('Socket reconnection error:', error);
      });

      socketInstance.on('reconnect_failed', () => {
        console.error('Socket reconnection failed');
        setIsConnected(false);
      });

      // Online users management
      socketInstance.on('users_online', (users: string[]) => {
        setOnlineUsers(users);
      });

      socketInstance.on('user_joined', (userId: string) => {
        setOnlineUsers(prev => [...prev.filter(id => id !== userId), userId]);
      });

      socketInstance.on('user_left', (userId: string) => {
        setOnlineUsers(prev => prev.filter(id => id !== userId));
      });

      setSocket(socketInstance);

      // Cleanup on unmount
      return () => {
        socketInstance.disconnect();
        setSocket(null);
        setIsConnected(false);
        setOnlineUsers([]);
      };
    }
  }, [session?.user?.id]);

  const value = {
    socket,
    isConnected,
    onlineUsers,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
