"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import ClientGuard from "@/components/ClientGuard";

const CoachDashboardPage = () => {
    const router = useRouter();

    useEffect(() => {
        // Redirect coaches directly to messages page
        router.push('/messages');
    }, [router]);

    return (
        <ClientGuard allowedRoles={[3]}>
            <div className="flex items-center justify-center h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Redirecting to Messages...</p>
                </div>
            </div>
        </ClientGuard>
    )
}
export default CoachDashboardPage