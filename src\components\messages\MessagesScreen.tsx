"use client";
import { useState, useEffect } from "react";
import ConversationList from "./ConversationList";
import ConnectedUsersList from "./ConnectedUsersList";
import UserIdSetup from "./UserIdSetup";
import { useChatMessages } from "@/hooks/useChatMessages";
import { ConnectedUser } from "@/hooks/useConnectedUsers";

export interface ApiChatConversation {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  galleries: { fileLocation: string }[];
}

const MessagesScreen = () => {
  const [selectedUser, setSelectedUser] = useState<ConnectedUser | null>(null);
  const [viewMode, setViewMode] = useState<'users' | 'conversations'>('users');
  const [showUserIdSetup, setShowUserIdSetup] = useState(false);
  const { conversations, messages, sendMessage } = useChatMessages();

  // Check if userId exists in localStorage
  useEffect(() => {
    const userId = localStorage.getItem('userId');
    setShowUserIdSetup(!userId);
  }, []);

  const handleSelectUser = (user: ConnectedUser) => {
    setSelectedUser(user);
    console.log('Selected user:', user);
  };

  const handleSelectConversation = (conv: any) => {
    // Handle conversation selection from the old system
    console.log('Selected conversation:', conv);
  };

  const handleSendMessage = (content: string, attachment?: { file: File; type: 'image' | 'document' | 'pdf'; url: string }) => {
    if (selectedUser) {
      // Convert user ID to string for compatibility
      sendMessage(selectedUser.id.toString(), content, attachment);
    }
  };

  // Show User ID setup if not configured
  if (showUserIdSetup) {
    return (
      <div className="flex h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-md w-full">
            <UserIdSetup />
            <div className="mt-4 text-center">
              <button
                onClick={() => setShowUserIdSetup(false)}
                className="text-sm text-blue-600 hover:text-blue-700 underline"
              >
                Skip for now (use mock data)
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Left Sidebar - Connected Users */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">Messages</h1>
            <div className="flex items-center space-x-3">
              <div className="flex space-x-2">
                <button
                  onClick={() => setViewMode('users')}
                  className={`px-3 py-1 text-xs rounded ${
                    viewMode === 'users'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Users
                </button>
                <button
                  onClick={() => setViewMode('conversations')}
                  className={`px-3 py-1 text-xs rounded ${
                    viewMode === 'conversations'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Chats
                </button>
              </div>
              <button
                onClick={() => setShowUserIdSetup(true)}
                className="text-xs text-gray-500 hover:text-gray-700 underline"
                title="User ID Settings"
              >
                Settings
              </button>
            </div>
          </div>
        </div>

        {viewMode === 'users' ? (
          <ConnectedUsersList
            onSelectUser={handleSelectUser}
            selectedUserId={selectedUser?.id}
          />
        ) : (
          <ConversationList
            conversations={conversations}
            selectedConversation={null}
            onSelectConversation={handleSelectConversation}
          />
        )}
      </div>

      {/* Right Side - Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedUser ? (
          <div className="flex flex-col h-full">
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-white">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-slate-700 text-white rounded-full flex items-center justify-center">
                  {selectedUser.firstName.charAt(0)}{selectedUser.lastName.charAt(0)}
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {selectedUser.firstName} {selectedUser.lastName}
                  </h2>
                  <p className="text-sm text-gray-500">{selectedUser.email}</p>
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
              <div className="space-y-4">
                {messages[selectedUser.id.toString()]?.map((message) => (
                  <div key={message.id} className="flex justify-end">
                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-2xl bg-blue-600 text-white">
                      <p className="text-sm">{message.content}</p>
                      <p className="text-xs mt-1 text-blue-100">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                )) || (
                  <div className="text-center text-gray-500 mt-8">
                    <p>Start a conversation with {selectedUser.firstName}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 bg-white">
              <form onSubmit={(e) => {
                e.preventDefault();
                const input = e.currentTarget.querySelector('input') as HTMLInputElement;
                if (input.value.trim()) {
                  handleSendMessage(input.value.trim());
                  input.value = '';
                }
              }} className="flex items-center space-x-2">
                <input
                  type="text"
                  placeholder={`Message ${selectedUser.firstName}...`}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Send
                </button>
              </form>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Select a user to start messaging
              </h3>
              <p className="text-gray-500">
                Choose a connected user from the list to start a conversation
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
export default MessagesScreen;
